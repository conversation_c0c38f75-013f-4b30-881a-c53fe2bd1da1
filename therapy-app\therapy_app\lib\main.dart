import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'screens/login_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/mood_entry_screen.dart';
import 'screens/therapist_list_screen.dart';
import 'screens/therapist_details_screen.dart';
import 'screens/appointments_screen.dart';
import 'screens/payment_method_screen.dart';
import 'screens/payment_confirmation_screen.dart';
import 'screens/analysis_screen.dart';
import 'screens/analysis_test_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/blocked_user_screen.dart';
import 'screens/therapist_application_screen.dart';

void main() {
  runApp(const TherapyApp());
}

final GoRouter _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/onboarding',
      builder: (context, state) => const OnboardingScreen(),
    ),
    GoRoute(
      path: '/mood-entry',
      builder: (context, state) => const MoodEntryScreen(),
    ),
    GoRoute(
      path: '/therapists',
      builder: (context, state) => const TherapistListScreen(),
    ),
    GoRoute(
      path: '/therapist-details',
      builder: (context, state) => const TherapistDetailsScreen(),
    ),
    GoRoute(
      path: '/appointments',
      builder: (context, state) => const AppointmentsScreen(),
    ),
    GoRoute(
      path: '/payment-method',
      builder: (context, state) => const PaymentMethodScreen(),
    ),
    GoRoute(
      path: '/payment-confirmation',
      builder: (context, state) => const PaymentConfirmationScreen(),
    ),
    GoRoute(
      path: '/analysis',
      builder: (context, state) => const AnalysisScreen(),
    ),
    GoRoute(
      path: '/analysis-test',
      builder: (context, state) => const AnalysisTestScreen(),
    ),
    GoRoute(
      path: '/settings',
      builder: (context, state) => const SettingsScreen(),
    ),
    GoRoute(
      path: '/blocked-user',
      builder: (context, state) => const BlockedUserScreen(),
    ),
    GoRoute(
      path: '/therapist-application',
      builder: (context, state) => const TherapistApplicationScreen(),
    ),
  ],
);

class TherapyApp extends StatelessWidget {
  const TherapyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Therapy App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        textTheme: GoogleFonts.poppinsTextTheme(),
        useMaterial3: true,
      ),
      routerConfig: _router,
    );
  }
}
