import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class AnalysisTestScreen extends StatefulWidget {
  const AnalysisTestScreen({super.key});

  @override
  State<AnalysisTestScreen> createState() => _AnalysisTestScreenState();
}

class _AnalysisTestScreenState extends State<AnalysisTestScreen> {
  int _currentQuestion = 0;
  List<int> _answers = [0, 0, 0, 0, 0];
  
  final List<String> _questions = [
    'Over the last 2 weeks, how often have you been bothered by feeling down, depressed, or hopeless?',
    'How often have you had little interest or pleasure in doing things?',
    'How often have you felt nervous, anxious, or on edge?',
    'How often have you been unable to stop or control worrying?',
    'How would you rate your overall stress level?',
  ];

  final List<List<String>> _options = [
    ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
    ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
    ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
    ['Not at all', 'Several days', 'More than half the days', 'Nearly every day'],
    ['Very low', 'Low', 'Moderate', 'High', 'Very high'],
  ];

  @override
  Widget build(BuildContext context) {
    final progress = (_currentQuestion + 1) / _questions.length;
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Mental Health Assessment',
          style: GoogleFonts.poppins(
            color: const Color(0xFF2C3E50),
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Question ${_currentQuestion + 1} of ${_questions.length}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      '${(progress * 100).round()}%',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF4A90E2),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[200],
                  valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
                  minHeight: 6,
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),
                  Text(
                    _questions[_currentQuestion],
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C3E50),
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 40),
                  ...List.generate(_options[_currentQuestion].length, (index) {
                    final isSelected = _answers[_currentQuestion] == index;
                    
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _answers[_currentQuestion] = index;
                          });
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isSelected ? const Color(0xFF4A90E2) : Colors.grey[300]!,
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            color: isSelected ? const Color(0xFF4A90E2).withOpacity(0.05) : Colors.white,
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected ? const Color(0xFF4A90E2) : Colors.grey[400]!,
                                    width: 2,
                                  ),
                                  color: isSelected ? const Color(0xFF4A90E2) : Colors.white,
                                ),
                                child: isSelected
                                    ? const Icon(
                                        Icons.check,
                                        size: 12,
                                        color: Colors.white,
                                      )
                                    : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  _options[_currentQuestion][index],
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    color: isSelected ? const Color(0xFF4A90E2) : const Color(0xFF2C3E50),
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
                  const Spacer(),
                  Row(
                    children: [
                      if (_currentQuestion > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              setState(() {
                                _currentQuestion--;
                              });
                            },
                            child: Text('Previous', style: GoogleFonts.poppins()),
                          ),
                        ),
                      if (_currentQuestion > 0) const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            if (_currentQuestion < _questions.length - 1) {
                              setState(() {
                                _currentQuestion++;
                              });
                            } else {
                              _showResults();
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF4A90E2),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            _currentQuestion < _questions.length - 1 ? 'Next' : 'Finish',
                            style: GoogleFonts.poppins(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showResults() {
    int totalScore = _answers.reduce((a, b) => a + b);
    String level;
    Color levelColor;
    String recommendation;

    if (totalScore <= 5) {
      level = 'Low';
      levelColor = const Color(0xFF50C878);
      recommendation = 'Your mental health appears to be in good shape. Keep up the healthy habits!';
    } else if (totalScore <= 10) {
      level = 'Moderate';
      levelColor = const Color(0xFFFFB347);
      recommendation = 'You may benefit from some stress management techniques and regular check-ins.';
    } else {
      level = 'High';
      levelColor = const Color(0xFFFF6B6B);
      recommendation = 'Consider speaking with a mental health professional for personalized support.';
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Column(
          children: [
            Icon(Icons.analytics, size: 48, color: levelColor),
            const SizedBox(height: 16),
            Text(
              'Assessment Results',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2C3E50),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: levelColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    'Stress Level: $level',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: levelColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Score: $totalScore/15',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              recommendation,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          if (totalScore > 10)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                context.go('/therapists');
              },
              child: Text('Find Therapist', style: GoogleFonts.poppins()),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.go('/analysis');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4A90E2),
              foregroundColor: Colors.white,
            ),
            child: Text('View Analysis', style: GoogleFonts.poppins()),
          ),
        ],
      ),
    );
  }
}
